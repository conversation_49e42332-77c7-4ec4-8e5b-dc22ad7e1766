package dev.pigmomo.yhkit2025.ui.screens

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.components.MonitoringChart
import dev.pigmomo.yhkit2025.ui.components.ChartConfigPanel
import dev.pigmomo.yhkit2025.ui.model.*
import dev.pigmomo.yhkit2025.ui.model.ChartConfig
import dev.pigmomo.yhkit2025.ui.model.ChartDataType
import dev.pigmomo.yhkit2025.ui.model.TimeRange
import dev.pigmomo.yhkit2025.ui.model.ChartDataSet
import dev.pigmomo.yhkit2025.ui.model.ChartDetailInfo
import dev.pigmomo.yhkit2025.viewmodel.MonitoringDataViewModel
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.delay
import androidx.compose.ui.Alignment
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控数据展示Screen
 * 用于展示所有监控商品的详细数据和变化记录
 * @param viewModel 监控数据视图模型
 * @param onNavigateBack 返回导航回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonitoringDataScreen(
    viewModel: MonitoringDataViewModel,
    onNavigateBack: () -> Unit
) {
    // 从ViewModel获取状态
    val monitoredProducts by viewModel.monitoredProducts.collectAsState()
    val isLoading by viewModel.isLoading
    val errorMessage by viewModel.errorMessage

    // 显示错误信息
    errorMessage?.let { message ->
        LaunchedEffect(message) {
            viewModel.clearErrorMessage()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Monitoring Data") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshData() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            // 显示商品列表
            ProductListScreen(
                products = monitoredProducts,
                viewModel = viewModel,
                modifier = Modifier.padding(paddingValues)
            )
        }
    }
}

/**
 * 商品列表页面
 */
@Composable
fun ProductListScreen(
    products: List<ProductMonitorEntity>,
    viewModel: MonitoringDataViewModel,
    modifier: Modifier = Modifier
) {
    // 管理展开的商品复合键（同时只能展开一个）
    var expandedProductKey by remember { mutableStateOf<String?>(null) }
    // 管理每个商品的变化记录
    val productChangeRecords =
        remember { mutableStateMapOf<String, List<ProductChangeRecordEntity>>() }
    // 管理每个商品的最近监控记录（用于未展开时显示）
    val productLatestRecords = remember { mutableStateMapOf<String, ProductChangeRecordEntity?>() }
    // 协程作用域
    val coroutineScope = rememberCoroutineScope()
    // LazyColumn 状态
    val listState = rememberLazyListState()

    // 商品列表
    LazyColumn(
        state = listState,
        userScrollEnabled = expandedProductKey == null, // 只有在没有展开卡片时才允许列表滚动
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        itemsIndexed(
            items = products,
            key = { _, product -> "${product.id}_${product.shopId}" }
        ) { index, product ->
            val productKey = "${product.id}_${product.shopId}"
            val isExpanded = expandedProductKey == productKey

            // 为每个商品加载最近的监控记录
            LaunchedEffect(product.id, product.shopId) {
                try {
                    Log.d(
                        "ProductListScreen",
                        "Loading change records for product ${product.id} in shop ${product.shopId}"
                    )
                    viewModel.getProductChangeRecords(product.id, product.shopId)
                        .collect { records ->
                            Log.d(
                                "ProductListScreen",
                                "Loaded ${records.size} change records for product ${product.id}"
                            )
                            val latestRecord =
                                records.maxByOrNull { it.changeTime }
                            if (latestRecord != null) {
                                Log.d(
                                    "ProductListScreen",
                                    "Latest record for ${product.id}: ${latestRecord.changeType} at ${latestRecord.changeTime}"
                                )
                            } else {
                                Log.d(
                                    "ProductListScreen",
                                    "No change records found for product ${product.id}"
                                )
                            }
                            // 总是更新最新记录，即使为null
                            productLatestRecords[productKey] = latestRecord
                        }
                } catch (e: Exception) {
                    Log.e(
                        "ProductListScreen",
                        "Failed to load latest record for product ${product.id}",
                        e
                    )
                    productLatestRecords[productKey] = null
                }
            }

            // 卡片缩放和透明度动画 - 使用更快的动画配置
            val cardAlpha by animateFloatAsState(
                targetValue = if (expandedProductKey == null || isExpanded) 1f else 0.4f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioNoBouncy,
                    stiffness = Spring.StiffnessHigh  // 更快的恢复速度
                ),
                label = "card_alpha"
            )

            val cardScale by animateFloatAsState(
                targetValue = if (expandedProductKey == null || isExpanded) 1f else 0.96f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioNoBouncy,
                    stiffness = Spring.StiffnessHigh  // 更快的恢复速度
                ),
                label = "card_scale"
            )

            // 卡片偏移动画，非展开卡片稍微向上偏移 - 更快的恢复动画
            val cardOffsetY by animateFloatAsState(
                targetValue = if (expandedProductKey == null || isExpanded) 0f else -4f,
                animationSpec = tween(
                    durationMillis = 150,  // 150ms恢复时间，更快的恢复
                    easing = EaseOutQuart
                ),
                label = "card_offset_y"
            )

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .graphicsLayer {
                        alpha = cardAlpha
                        scaleX = cardScale
                        scaleY = cardScale
                        translationY = cardOffsetY
                    }
                    .animateContentSize(
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioNoBouncy,
                            stiffness = Spring.StiffnessHigh  // 更快的内容大小变化速度
                        )
                    )
                    // 当有其他卡片展开时，禁用当前卡片的交互
                    .then(
                        if (expandedProductKey != null && !isExpanded) {
                            Modifier.clickable(
                                indication = null,
                                interactionSource = remember { MutableInteractionSource() }
                            ) { /* 禁用点击 */ }
                        } else {
                            Modifier
                        }
                    )
            ) {
                ProductListItem(
                    product = product,
                    isExpanded = isExpanded,
                    changeRecords = productChangeRecords[productKey] ?: emptyList(),
                    latestRecord = productLatestRecords[productKey],
                    viewModel = viewModel,
                    onToggleExpand = {
                        // 只有在没有其他卡片展开，或者当前卡片已展开时才允许操作
                        if (expandedProductKey == null || isExpanded) {
                            if (isExpanded) {
                                // 收起
                                expandedProductKey = null
                                productChangeRecords.remove(productKey)
                            } else {
                                // 展开
                                expandedProductKey = productKey
                                // 智能滚动处理
                                coroutineScope.launch {
                                delay(100) // 让展开动画开始

                                // 计算合适的滚动位置
                                val totalItems = products.size
                                val isNearBottom = index >= totalItems - 3

                                if (isNearBottom) {
                                    // 如果是底部附近的卡片，滚动到列表末尾
                                    listState.animateScrollToItem(
                                        index = totalItems - 1,
                                        scrollOffset = 0
                                    )
                                } else {
                                    // 否则滚动到当前卡片，留出顶部空间
                                    listState.animateScrollToItem(
                                        index = index,
                                        scrollOffset = -150
                                    )
                                }
                            }
                                // 加载变化记录
                                coroutineScope.launch {
                                try {
                                    val records = viewModel.getProductChangeRecords(
                                        product.id,
                                        product.shopId
                                    )
                                    records.collect { recordList ->
                                        productChangeRecords[productKey] =
                                            recordList.sortedByDescending { it.changeTime }
                                    }
                                } catch (e: Exception) {
                                    Log.e(
                                        "ProductListScreen",
                                        "Failed to load change records for product ${product.id}",
                                        e
                                    )
                                }
                                }
                            }
                        }
                    },
                    onDeleteProduct = { productId, shopId ->
                        // 删除商品监控记录
                        viewModel.deleteProduct(productId, shopId)
                        // 如果当前展开的是被删除的商品，则收起
                        val deletedProductKey = "${productId}_${shopId}"
                        if (expandedProductKey == deletedProductKey) {
                            expandedProductKey = null
                            productChangeRecords.remove(deletedProductKey)
                            productLatestRecords.remove(deletedProductKey)
                        }
                    },
                    dateFormat = viewModel.dateFormat,
                    fullDateFormat = viewModel.fullDateFormat
                )
            }
        }

        // 添加底部占位符，确保最后的卡片展开时有足够空间
        item {
            val configuration = LocalConfiguration.current
            val screenHeight = configuration.screenHeightDp.dp

            // 计算动态底部间距
            val bottomPadding = if (expandedProductKey != null) {
                // 找到展开卡片的索引
                val expandedIndex = products.indexOfFirst { product ->
                    "${product.id}_${product.shopId}" == expandedProductKey
                }

                // 根据展开卡片的位置调整间距
                when (expandedIndex) {
                    -1 -> 0.dp // 未找到展开卡片，使用默认间距

                    products.size - 1 -> {
                        // 最后1个卡片需要更多空间
                        screenHeight * 0.70f
                    }

                    products.size - 2 -> {
                        // 倒数第2个卡片需要中等空间
                        screenHeight * 0.58f
                    }

                    products.size - 3 -> {
                        // 倒数第3个卡片需要中等空间
                        screenHeight * 0.45f
                    }

                    products.size - 4 -> {
                        // 倒数第4个卡片需要中等空间
                        screenHeight * 0.3f
                    }

                    else -> {
                        // 其他位置的卡片需要少量空间
                        screenHeight * 0.15f
                    }
                }
            } else {
                // 正常情况下的小间距
                0.dp
            }

            // 使用动画平滑过渡间距变化
            val animatedPadding by animateDpAsState(
                targetValue = bottomPadding,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioNoBouncy,
                    stiffness = Spring.StiffnessMedium
                ),
                label = "bottom_padding"
            )

            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(animatedPadding)
            )
        }
    }
}


/**
 * 商品列表项组件
 */
@SuppressLint("DefaultLocale")
@Composable
fun ProductListItem(
    product: ProductMonitorEntity,
    isExpanded: Boolean,
    changeRecords: List<ProductChangeRecordEntity>,
    latestRecord: ProductChangeRecordEntity? = null,
    viewModel: MonitoringDataViewModel,
    onToggleExpand: () -> Unit,
    onDeleteProduct: (String, String) -> Unit,
    dateFormat: SimpleDateFormat,
    fullDateFormat: SimpleDateFormat
) {
    // 删除确认对话框状态
    var showDeleteDialog by remember { mutableStateOf(false) }

    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioNoBouncy,
                    stiffness = Spring.StiffnessMediumLow
                )
            )
    ) {
        Column {
            // 主要信息区域 - 点击展开/收起
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        enabled = true, // 始终允许点击展开/收起
                        onClick = onToggleExpand
                    )
                    .padding(horizontal = 12.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 商品标题
                Text(
                    text = product.title,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    modifier = Modifier
                        .weight(1f)
                        .horizontalScroll(rememberScrollState())
                )

                // 操作按钮区域
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 删除按钮
                    IconButton(
                        onClick = { showDeleteDialog = true },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除监控",
                            tint = Color(0xFFF44336),
                            modifier = Modifier.size(18.dp)
                        )
                    }

                    // 展开图标（带动画）
                    val rotationAngle by animateFloatAsState(
                        targetValue = if (isExpanded) 180f else 0f,
                        animationSpec = tween(
                            durationMillis = 300,
                            easing = FastOutSlowInEasing
                        ),
                        label = "arrow_rotation"
                    )

                    // 展开图标（不需要单独的点击处理，因为整个Row都可以点击）
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "展开详情",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier
                            .size(18.dp)
                            .rotate(rotationAngle)
                    )
                }
            }

            // SKU、库存、店铺信息行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 2.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.Bottom
            ) {
                Text(
                    text = "SKU: ${product.id} ${if (product.originalSkuCode.isNotEmpty() && product.originalSkuCode != product.id) "(${product.originalSkuCode})" else ""}",
                    fontSize = 11.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.alignByBaseline()
                )
                Text(
                    text = "库存: ${if (product.stockNum > 0) "${product.stockNum / 100}" else "未知"}",
                    fontSize = 11.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.alignByBaseline()
                )
                if (product.shopId.isNotEmpty()) {
                    Text(
                        text = "店铺: ${product.shopName}",
                        fontSize = 11.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier
                            .alignByBaseline()
                            .horizontalScroll(rememberScrollState())
                    )
                }
            }

            // 价格和状态标签行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 4.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 价格信息
                if (product.currentPrice > 0) {
                    Text(
                        text = "¥${String.format("%.2f", product.currentPrice / 100.0)}",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                } else {
                    Text(
                        text = "暂无价格",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // 状态标签组
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 秒杀标签
                    if (product.isSeckill == 1) {
                        Surface(
                            color = Color(0xFFFF9800),
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = "秒杀",
                                color = Color.White,
                                fontSize = 10.sp,
                                lineHeight = 10.sp,
                                modifier = Modifier.padding(horizontal = 6.dp)
                            )
                        }
                    }

                    // 可用性状态标签
                    when {
                        product.available == 0 -> {
                            Surface(
                                color = Color(0xFF9E9E9E),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "下架",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }

                        product.canNotBuy -> {
                            Surface(
                                color = Color(0xFFF44336),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "不可购买",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }

                        product.stockNum == 0 -> {
                            Surface(
                                color = Color(0xFFF44336),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "缺货",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }

                        product.available == 1 && product.stockNum > 0 -> {
                            Surface(
                                color = Color(0xFF4CAF50),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "有货",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }
                    }

                    // 限购标签
                    if (product.restrictLimit > 0) {
                        Surface(
                            color = Color(0xFFFF9800),
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = "限购${product.restrictLimit / 100}",
                                color = Color.White,
                                fontSize = 10.sp,
                                lineHeight = 10.sp,
                                modifier = Modifier.padding(horizontal = 6.dp)
                            )
                        }
                    }

                    // 最近监控记录信息（仅在未展开且有记录时显示）
                    if (latestRecord != null) {
                        Surface(
                            color = when (latestRecord.changeType) {
                                ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                                ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                                ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                                ProductChangeType.INFO_CHANGE -> Color(0xFF9C27B0)
                                ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                                ProductChangeType.RESTRICT_CHANGE -> Color(0xFF795548)
                                else -> Color(0xFF607D8B)
                            },
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                when (latestRecord.changeType) {
                                    ProductChangeType.PRICE_CHANGE -> "价格"
                                    ProductChangeType.STOCK_CHANGE -> "库存"
                                    ProductChangeType.AVAILABILITY_CHANGE -> "可用性"
                                    ProductChangeType.INFO_CHANGE -> "信息"
                                    ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀"
                                    ProductChangeType.RESTRICT_CHANGE -> "限购"
                                    else -> "其他"
                                } + " " + formatLatestChangeInfo(latestRecord) + " " + dateFormat.format(
                                    latestRecord.changeTime
                                ),
                                color = Color.White,
                                fontSize = 10.sp,
                                lineHeight = 10.sp,
                                modifier = Modifier.padding(horizontal = 6.dp)
                            )
                        }
                    }
                }
            }

            // 展开的详细信息（平滑展开动画）
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessMediumLow
                    )
                ) + fadeIn(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                ),
                exit = shrinkVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeOut(
                    animationSpec = tween(
                        durationMillis = 250,
                        easing = FastOutSlowInEasing
                    )
                )
            ) {
                val configuration = LocalConfiguration.current
                val screenHeight = configuration.screenHeightDp.dp
                val maxCardHeight = screenHeight * 0.7f // 卡片最大高度为屏幕高度的70%

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = maxCardHeight) // 根据屏幕高度动态调整
                        .verticalScroll(rememberScrollState()) // 添加垂直滚动
                        .padding(horizontal = 12.dp, vertical = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 分隔线
                    HorizontalDivider(
                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f),
                        thickness = 1.dp
                    )

                    // 监控状态信息
                    Surface(
                        color = if (product.isMonitoringEnabled) Color(0xFF4CAF50).copy(alpha = 0.1f) else Color(
                            0xFFF44336
                        ).copy(alpha = 0.1f),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = if (product.isMonitoringEnabled) "监控中 - 上次更新: ${
                                dateFormat.format(
                                    product.lastUpdateTime
                                )
                            }" else "已停止 - 上次更新: ${dateFormat.format(product.lastUpdateTime)}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (product.isMonitoringEnabled) Color(0xFF4CAF50) else Color(
                                0xFFF44336
                            ),
                            modifier = Modifier.padding(12.dp)
                        )
                    }

                    // 图表展示区域
                    ChartSection(
                        product = product,
                        viewModel = viewModel
                    )

                    // 变化记录标题和数量
                    if (changeRecords.isNotEmpty()) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "监控记录",
                                style = MaterialTheme.typography.titleSmall,
                                fontWeight = FontWeight.Bold
                            )
                            Surface(
                                color = Color(0xFF2196F3).copy(alpha = 0.1f),
                                shape = RoundedCornerShape(12.dp)
                            ) {
                                Text(
                                    text = "${changeRecords.size}条记录",
                                    style = MaterialTheme.typography.labelMedium,
                                    color = Color(0xFF2196F3),
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                )
                            }
                        }

                        // 变化记录列表（带交错动画，限制高度）
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = 200.dp), // 减少最大高度为200dp，为图表留出更多空间
                            verticalArrangement = Arrangement.spacedBy(4.dp),
                            userScrollEnabled = true // 确保记录列表可以滚动
                        ) {
                            itemsIndexed(
                                items = changeRecords,
                                key = { _, record -> record.id }
                            ) { index, record ->
                                AnimatedVisibility(
                                    visible = true,
                                    enter = slideInVertically(
                                        animationSpec = tween(
                                            durationMillis = 300,
                                            delayMillis = index * 50,
                                            easing = FastOutSlowInEasing
                                        ),
                                        initialOffsetY = { it / 4 }
                                    ) + fadeIn(
                                        animationSpec = tween(
                                            durationMillis = 300,
                                            delayMillis = index * 50,
                                            easing = FastOutSlowInEasing
                                        )
                                    )
                                ) {
                                    ChangeRecordCompactItem(
                                        record = record,
                                        dateFormat = fullDateFormat
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("确认删除") },
            text = {
                Text("确定要删除商品「${product.title}」的监控记录吗？")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        onDeleteProduct(product.id, product.shopId)
                    }
                ) {
                    Text("删除", color = Color(0xFFF44336))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = false }
                ) {
                    Text("取消")
                }
            },
            containerColor = dialogContainerColor()
        )
    }
}

/**
 * 紧凑的变化记录项组件
 */
@Composable
fun ChangeRecordCompactItem(
    record: ProductChangeRecordEntity,
    dateFormat: SimpleDateFormat
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp, vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.weight(1f),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 变化类型标签
            Surface(
                color = when (record.changeType) {
                    ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                    ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                    ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                    ProductChangeType.INFO_CHANGE -> Color(0xFF9C27B0)
                    ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                    ProductChangeType.RESTRICT_CHANGE -> Color(0xFFFFCDD2)
                    else -> Color(0xFF607D8B)
                },
                shape = RoundedCornerShape(4.dp)
            ) {
                Text(
                    text = when (record.changeType) {
                        ProductChangeType.PRICE_CHANGE -> "价格"
                        ProductChangeType.STOCK_CHANGE -> "库存"
                        ProductChangeType.AVAILABILITY_CHANGE -> "可用性"
                        ProductChangeType.INFO_CHANGE -> "信息"
                        ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀"
                        ProductChangeType.RESTRICT_CHANGE -> "限购"
                        else -> "其他"
                    },
                    color = Color.White,
                    fontSize = 8.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp)
                )
            }

            // 变化内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                if (record.oldValue.isNotEmpty() || record.newValue.isNotEmpty()) {
                    Text(
                        text = "${record.oldValue} → ${record.newValue}",
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.primary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        fontWeight = FontWeight.Medium
                    )
                } else if (record.changeDescription.isNotEmpty()) {
                    Text(
                        text = record.changeDescription,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }

        // 时间
        Text(
            text = dateFormat.format(record.changeTime),
            fontSize = 9.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 将详情key转换为中文显示
 */
private fun translateDetailKey(key: String): String {
    return when (key) {
        "changeType" -> "变化类型"
        "oldValue" -> "原值"
        "newValue" -> "新值"
        "description" -> "变化描述"
        else -> key
    }
}

/**
 * 格式化最近变化信息，用于卡片中的简化显示
 */
private fun formatLatestChangeInfo(record: ProductChangeRecordEntity): String {
    return when (record.changeType) {
        ProductChangeType.PRICE_CHANGE -> {
            when {
                record.oldValue.isNotEmpty() && record.newValue.isNotEmpty() -> {
                    "${record.oldValue}->${record.newValue}"
                }

                record.newValue.isNotEmpty() -> record.newValue
                else -> "价格变化"
            }
        }

        ProductChangeType.STOCK_CHANGE -> {
            when {
                record.oldValue.isNotEmpty() && record.newValue.isNotEmpty() -> {
                    "${record.oldValue}->${record.newValue}"
                }

                record.newValue.isNotEmpty() -> record.newValue
                else -> "库存变化"
            }
        }

        ProductChangeType.AVAILABILITY_CHANGE -> {
            when {
                record.oldValue.isNotEmpty() && record.newValue.isNotEmpty() -> {
                    val oldStatus = if (record.oldValue == "1") "可用" else "不可用"
                    val newStatus = if (record.newValue == "1") "可用" else "不可用"
                    "$oldStatus->$newStatus"
                }

                record.newValue.isNotEmpty() -> {
                    if (record.newValue == "1") "可用" else "不可用"
                }

                else -> "可用性变化"
            }
        }

        ProductChangeType.SECKILL_STATUS_CHANGE -> {
            when {
                record.oldValue.isNotEmpty() && record.newValue.isNotEmpty() -> {
                    val oldStatus = if (record.oldValue == "1") "是" else "否"
                    val newStatus = if (record.newValue == "1") "是" else "否"
                    "$oldStatus->$newStatus"
                }

                record.newValue.isNotEmpty() -> {
                    if (record.newValue == "1") "已秒杀" else "非秒杀"
                }

                else -> "秒杀状态变化"
            }
        }

        ProductChangeType.RESTRICT_CHANGE -> {
            when {
                record.oldValue.isNotEmpty() && record.newValue.isNotEmpty() -> {
                    "${record.oldValue}->${record.newValue}"
                }

                record.newValue.isNotEmpty() -> record.newValue
                else -> "限购变化"
            }
        }

        ProductChangeType.INFO_CHANGE -> {
            when {
                record.oldValue.isNotEmpty() && record.newValue.isNotEmpty() -> {
                    "${record.oldValue}->${record.newValue}"
                }

                record.newValue.isNotEmpty() -> record.newValue
                record.changeDescription.isNotEmpty() -> record.changeDescription
                else -> "信息变化"
            }
        }

        ProductChangeType.OTHER_CHANGE -> {
            when {
                record.oldValue.isNotEmpty() && record.newValue.isNotEmpty() -> {
                    "${record.oldValue}->${record.newValue}"
                }

                record.newValue.isNotEmpty() -> record.newValue
                record.changeDescription.isNotEmpty() -> record.changeDescription
                else -> "其他变化"
            }
        }
    }
}

/**
 * 图表展示区域
 */
@Composable
fun ChartSection(
    product: ProductMonitorEntity,
    viewModel: MonitoringDataViewModel
) {
    // 图表配置状态
    var chartConfig by remember {
        mutableStateOf(
            ChartConfig(
                dataType = ChartDataType.STOCK,
                timeRange = TimeRange.TODAY
            )
        )
    }

    // 图表数据状态
    val chartDataSet by viewModel.generateChartDataSet(
        productId = product.id,
        shopId = product.shopId,
        config = chartConfig
    ).collectAsState(
        initial = ChartDataSet(
            dataPoints = emptyList(),
            config = chartConfig
        )
    )

    // 详情对话框状态
    var showDetailDialog by remember { mutableStateOf(false) }
    var selectedDetailInfo by remember { mutableStateOf<ChartDetailInfo?>(null) }

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 图表配置面板
        ChartConfigPanel(
            config = chartConfig,
            onConfigChange = { newConfig ->
                chartConfig = newConfig
            }
        )

        // 图表组件
        MonitoringChart(
            dataSet = chartDataSet,
            onDataPointClick = { detailInfo ->
                selectedDetailInfo = detailInfo
                showDetailDialog = true
            }
        )
    }

    // 详情对话框
    if (showDetailDialog && selectedDetailInfo != null) {
        ChartDetailDialog(
            detailInfo = selectedDetailInfo!!,
            onDismiss = {
                showDetailDialog = false
                selectedDetailInfo = null
            }
        )
    }
}

/**
 * 图表详情对话框
 */
@Composable
fun ChartDetailDialog(
    detailInfo: ChartDetailInfo,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("数据详情")
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "时间: ${detailInfo.formattedTime}",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "数值: ${detailInfo.formattedValue}",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )

                if (detailInfo.details.isNotEmpty()) {
                    HorizontalDivider(
                        modifier = Modifier.padding(vertical = 4.dp),
                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
                    )

                    detailInfo.details.forEach { (key, value) ->
                        if (value.isNotEmpty()) {
                            Text(
                                text = "${translateDetailKey(key)}: $value",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        },
        containerColor = dialogContainerColor()
    )
}
